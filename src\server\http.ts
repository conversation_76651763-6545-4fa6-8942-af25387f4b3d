import {
  get,
  post,
  httpRequest,
  downloadFile,
  uploadFile,
} from '@/utils/request';

const ajax = {
  get: (url: string, data: any, params: any, options: any) =>
    get(url, data, options),
  post,
  get_http: (url: string, data: any) => httpRequest('get', url, data),
  post_http: (url: string, data: any) => httpRequest('post', url, data),
  getFile: downloadFile,
  uploadFile,
};

const INTERFACES = {
  login: `post /auth/login`,
  // 通用接口
  /** 用户信息 */
  user_info: `post /resource/user/info`,
  /** 销售信息 */
  sale_info: `post /fund_auth/sale/info`,
  /** 用户权限 */
  user_admin: `get /resource/user/list`,
  /** 小组列表 */
  sale_group: `get /common/get/v1/sale_group`,
  // 首页数据看板
  home_board: `post /homepage/over/view`,
  home_table_total: `post /homepage/group/list`,
  home_table_group: `post /homepage/sale/list`,
  // 人事相关-工资核算
  person_salary_table: `post /salary/sales/list`,
  // person_salary_excel: `post /salary/export/sales/salaryexcel`,
  person_salary_excel: `getFile /salary/export/salaryexcel`,
  // 销售相关-销售列表
  sale_list_table: `post /sale_list/get/sale_page`,
  // 运营相关-产品列表
  get_recommend_level: `get /invest_product/get/recommend_level`,
  /** 删除产品记录 */
  operate_delete: `get /invest_product/delete/product`,
  // 高端理财
  operate_get_gdlc: `post /invest_product/get/top_page`,
  operate_get_detail_id_gdlc: `get /invest_product/get/top_detail`,
  operate_get_detail_code_gdlc: `get /invest_product/push/top`,
  operate_add_gdlc: `post /invest_product/add/top`,
  // 私募
  operate_get_sm: `post /invest_product/get/private_page`,
  operate_get_detail_id_sm: `get /invest_product/get/private_detail`,
  operate_get_detail_code_sm: `get /invest_product/push/private`,
  operate_add_sm: `post /invest_product/add/private`,
  // 公募
  operate_get_gm: `post /invest_product/get/public_page`,
  operate_get_detail_id_gm: `get /invest_product/get/public_detail`,
  operate_get_detail_code_gm: `get /invest_product/push/public`,
  operate_add_gm: `post /invest_product/add/public`,
  // 销售相关
  // 销售活动列表
  fetchSaleActivity: `post /amount/activity/v1/page`,
  addSaleActivity: `post /amount/activity/v1/add`,
  editSaleActivity: `post /amount/activity/v1/edit`,
  changeSaleActivityState: `post /amount/activity/v1/state`,
  // 产品-销售活动相关
  contractSaleActivity: `post /amount/activity/v1/related`,
  fetchProductionActivity: `post /amount/activity/v1/relatedinfo`,
  editProductionActivity: `post /amount/activity/v1/disassociation`,
  // 客户-销售活动信息
  fetchUserActivity: `post /amount/activity/v1/cust_relatedinfo`,
  /** 销售相关 - 获取统计人员 */
  getSalesManList: `get /common/get/sale_tree`,
  /** 销售相关 - 获取系统当前销售人员 */
  getSalesMan: `get /resource/user/info`,
  /** 获取登录用户信息 */
  postUserInfo: `post /resource/user/info`,
  /** 获取销售的等级信息 */
  // postSalesManGrade: `post /fund_auth/sale/info`,
  /** 获取登录用户资源 */
  getUserPower: `post /resource/user/list`,
  /** 业绩统计 */
  postKpiCensusData: `post /achievement/census/data`,
  /** 电话统计 */
  postTelCensusData: `post /tel/census/data`,
  /** 销售小组 - 业绩 */
  postChargeKpiData: `post /sales/performance/team`,
  /** 销售小组 - 数据总览 */
  postChargeData: `post /sales/overview/team`,
  /** 销售经理 - 业绩统计 */
  // postManagerKpiData: `post /sales/performance/single`,
  postManagerKpiData: `get /sale_profit/sales/v1/id_list`,
  /** 销售经理 - 数据总览 */
  postManagerData: `post /sales/overview/single`,
  /** 销售经理 - 电话记录 */
  postTelAllData: `post /sales/tel/total`,
  postTelContactData: `post /sales/tel/record`,
  /** 销售经理 - 销售记录 */
  postSaleAllData: `post /sales/sales/total`,
  postSaleRecordData: `post /sales/sales/record`,
  // 2022.6.16 新版销售分成
  /** 个人业绩查询 */
  getSaleKpi: `get /sale_profit/sales/v1/group_list`,
  /** 小组业绩查询 */
  getGroupKpi: `get /sale_profit/sales/v1/all_group_list`,
  /** 个人业绩导出 */
  exportSaleKpi: `getFile /sale_profit/export/v1/group_excel`,
  /** 小组业绩导出 */
  exportGroupKpi: `getFile /sale_profit/export/v1/all_group_excel`,
  /** 具体销售提成明细 */
  getSaleKpiDetails: `get /sale_profit/sales/v1/id_detail`,
  /** 销售主管考核数据 - 查询 */
  getSaleLeaderConfig: `get /sale_group_leader/v1/select`,
  /** 销售主管考核数据 - 保存 */
  postSaleLeaderConfig: `post /sale_group_leader/v1/update`,
  // 客户相关 - 客户分配
  /** 获取数据 */
  postCustList: `post /customer/get/distribute_page`,
  /** 获取资料归属树 */
  getMaterialTree: `get /customer/get/belong_tree`,
  /** 获取资料类型及客户等级 */
  getMaterialType: `get /customer/get/logo`,
  /** 获取客户申请原因下拉列表 */
  getApplyReasonList: `get /common/param/v1/get_param_config`,
  /** 分配资料 */
  postCustDis: `post /customer/distribute/customer`,
  /** 申请客户 */
  applyCustomer: `post /customer/belong/v1/apply`,
  /** 删除资料 */
  postCustdel: `post /customer/recycle/customer`,
  // 客户相关 - 客户列表
  postCustInfo: `post /customer/get/page`,
  postCustRemark: `get /customer/edit/customer_remark`,
  // 客户相关 - 客户详情
  // 详细资料
  getCustDel: `get /customer/get/detail`,
  getAssetsPicture: `get /customer/account/v1/get_assets_picture`,
  getCommonWords: `get /customer/account/v1/get_common_words`,
  postCustRecord: `post /customer/get/contact_page`,
  editSubNumber: `post /customer/edit/phone`,
  addPhoneNumber: `post /customer/add/phone`,
  delPhoneNumber: `get /customer/supply/v1/del_phone`,
  editWeChat: `post /customer/edit/wechat`,
  addNote: `post /customer/add/contact_note`,
  addUserRecord: `post /customer/add/contact_log`,
  getPhoneBase64: `get /customer/get/phone_base64`,
  updatePhoneNumber: `post /customer/update/newest_phone`,
  getActivitySource: `post /customer/v1/last_source`,
  getActivityRecordList: `post /customer/v1/get_source_list`,
  updateIsWX: `post /customer/account/v1/is_weixin`,
  updateAccountNote: `post /customer/account/v1/account_note`,
  getWXMobile: `get /wechat/v1/get_mobile_str`,
  getUserHobbys: `get /customer/account/v1/get_preference_list`,
  AddUserHobbys: `post /customer/account/v1/add_preference`,
  getPhoneDetails: `get /sale_call/v1/query_record`,
  getAllocateLog: `get /customer/supply/v1/allocate/log`,
  getContactInfo: `get /customer/supply/v1/contact_info`,
  getCustResourceList: `post /customer/database/v1/cust_list/search`,
  getUserTags: `get /arsenal/tag/v1`,
  getViewPermission: `get /customer/belong/v1/check/detail/access`,
  // 未开户用户详情页
  getUserDetail: `get /customer/v1/non_open/detail`, // 通过userid查询用户详情
  noOpenUserAddRecord: `post /customer/add/non_open/contact_log`, // 新增联系记录
  noOpenUserContactRecords: `post /customer/get/non_open/contact_page`, // 未开户用户网销联系记录
  noOpenUserPhoneRecords: `get /sale_call/v2/query_record`, // 未开户用户电话联系记录
  getOnlineSaleCustomerGrade: `get /customer/database/v1/query_term`, // 获取网销维度客户等级列表
  // 买卖记录
  getTradeProdList: `post /trade/cfm/bought/productList`,
  getTradeTableData: `post /trade/cfm/deal/record`,
  getTradeChartData: `post /trade/cfm/line/chart`,
  // 持仓明细
  getHoldTableData: `post /cust/position/v1/history`,
  // 净增量明细
  getNetRecordTable: `get /cust_increment/v1/record`,
  exportNetRecordTable: `getFile /cust_increment/v1/record_export`, // 导出净增量记录

  // 客服相关
  // 客服相关 - 意见反馈
  getFeedbackTags: `get /cust/service/get/tag`,
  getFeedbackTableData: `post /cust/service/get/page`,
  replyFeedback: `post /cust/service/add/reply`,
  // 客服相关 - 私募回访
  getSimuTable: `post /pefund/v1/getPage`,
  updateSimuTable: `post /pefund/v1/updatepefund`,
  getSimuFile: `getFile /pefund/v1/exportpage`,
  // 红包后台
  // 红包后台 - 活动项目配置列表
  getProjectTableData: `post /redpacket/item/page`, // 查询项目列表
  editProject: `post /redpacket/item/edit`, // 编辑项目/资金申请
  getBudgetDetail: `get /redpacket/item/detail`, // 获取项目资金细节
  getBudgetTableData: `get /redpacket/item/apply/detail`, // 获取项目资金申请列表
  postBudgetVerify: `post /redpacket/item/audit`,
  getTotalAmount: `get /redpacket/item/amount/stats`,
  // 项目下拉列表
  getProjectList: `get /redpacket/item/v1/project_list`,
  getProjectListForDropdown: `get /redpacket/item/v1/project_list`,
  // 部门下拉列表
  getDepartmentListForDropdown: `get /redpacket/item/v1/department_list`,
  // 获取部门负责人
  getDepartmentAdmin: `get /redpacket/item/v1/department_admin`,
  // 红包后台 - 销售统计
  getSaleStatisticTable: `post /redpacket/item/sale/stats`,
  // 红包后台 - 销售红包库
  getSaleRedTable: `post /redpacket/item/sale/storehouse`,
  // 红包后台 - 红包使用查询
  getRedQueryTable: `post /redpacket/item/used/query`,
  exportPacketUsedFile: `getFile /redpacket/item/used/export`,
  // 券模板
  // 券模板分页
  fetchCouponTemplateTable: `get /coupon/template/v1/list`,
  // 券模板-详情
  fetchCouponTemplateDetail: `post /coupon/template/v1/select_one`,
  // 券模板-新增
  couponTemplateAdd: `post /coupon/template/v1/add`,
  // 券模板-大修改
  couponTemplateEditAll: `post /coupon/template/v1/edit`,
  // 券模板-小修改
  couponTemplateEditPart: `post /coupon/template/v1/edit_part`,
  // 券模板-状态/审核状态-修改
  postCouponTemplateStatus: `post /coupon/template/v1/approve`,
  // 券模板列表
  getCouponTemplate: `get /coupon/template/v1/select_list`,

  // 券活动
  // 获取全量基金公司
  getAllCompany: `post /coupon/tacode/all`,
  // 券活动列表分页
  fetchCouponList: `post /coupon/list`,
  // 券活动-详情
  fetchCouponDetail: `post /coupon/get`,
  // 券活动-新增
  postCouponAdd: `post /coupon/add`,
  // 券活动-大修改
  postCouponEditAll: `post /coupon/update`,
  // 券活动-小修改
  postCouponEditPart: `post /coupon/update/part`,
  // 券活动-状态/审核状态-修改
  postCouponStatus: `post /coupon/approve`,
  // 红包组新增
  addCouponGroup: `post /coupon/group/add`,
  // 红包组分页请求
  fetchCouponGroup: `post /coupon/group/list`,
  // 红包组全量请求
  fetchCouponGroupAll: `post /coupon/group/all`,

  // 各明细页
  // 活动明细
  getActivityTable: `post /coupon/census/activity/list`,
  // 红包明细
  getRedDetailTable: `post /coupon/census/redpacket/list`,
  // 领取明细
  getReceiveDetailTable: `post /coupon/census/received/list`,
  // 使用明细
  getUseDetailTable: `post /coupon/census/used/list`,
  // 核销明细
  getPacketUsedTable: `post /redpacket/item/used/page`,
  // 2022.5.20 份额明细
  getExpectedPortionTable: `post /fund/shares/v1/expected_list`, // 应发明细
  getActualPortionTable: `post /fund/shares/v1/actual_list`, // 实发明细
  exportPortionDetail: `getFile /fund/shares/v1/export`, // 导出实发明细

  // 黑名单
  // 黑名单分页
  fetchBlackMenuTable: `get /black_list/v1/query`,
  // 黑名单移除
  removeBlackMenu: `post /black_list/v1/delete`,
  // 黑名单导出
  exportBlackMenu: `getFile /black_list/v1/export`,
  // 添加黑名单
  addBlackMenu: `post /black_list/v1/import`,
  // 黑名单示例
  downloadBlackMenuExample: `getFile /black_list/v1/template`,

  // 财务相关
  financial_reconReport_list_table: `get /finance/v1/sale_record`, // 对账表
  export_financial_reconReport: `getFile /finance/v1/sale_record_export`, // 导出对账表
  export_financial_reconReport_detail: `getFile /finance/v1/cust_list_export`, // 导出销售对应提成客户
  financial_reconReport_detail_list_table: `get /finance/v1/cust_list`, // 销售对应的提成客户表
  customer_specificInfo_list_table: `get /finance/v1/cust_record`, // 客户具体明细
  product_params_list_table: `get /finance/v1/fund_param`, // 产品参数
  export_cust_record: `getFile /finance/v1/cust_record_export`, // 客户具体明细导出
  getCapitalFlowReport: `get /clearing/counter/report/yqzl/v1/summary`, // 银企直连流水汇总查询
  exportCapitalFlowReport: `getFile /clearing/counter/report/yqzl/v1/exportsummary`, // 导出银企直连流水汇总
  getCapitalFlowDetail: `get /clearing/counter/report/yqzl/v1/detail`, // 银企直连流水明细报表查询
  exportCapitalFlowDetail: `getFile /clearing/counter/report/yqzl/v1/exportdetail`, // 导出银企直连流水明细报表
  // 电销推送资料
  clues_activity_list: `get /tracking/data/v1/get_activity_list`, // 活动下拉列表
  clues_activity_tree: `get /tracking/data/v1/get_activity_tree`, // 活动来源树状列表
  clues_table_list: `post /tracking/data/v1/get_list`, // 查询列表
  clues_export: `getFile /tracking/data/v1/export`, // 导出
  clues_distri_rule: `post /tracking/data/v1/edit_allocate_rule`, // 分配规则
  // 网销分成方案
  d_pool_table_list: `get /wechat/bind/v1/account_info_list`, // 获取表格数据
  d_pool_sale_tree: `get /common/sale_tree/v1/list_auth`, // 获取销售树
  // 客服联系小计
  get_kefu_log_config: `get /customer/v1/get_kefu_log_config`, // 获取客服联系配置项
  get_all_old_url: `get /oldcrm/v1/get_all_url`, // 获取CRM老平台url

  // 小喇叭配置
  get_horn_config: 'get /horn/v1/get_config',
  // 获取小喇叭活动下拉列表
  get_horn_activity_enum: 'get /tracking/data/v1/get_activity_list',
  // 获取小喇叭来源下拉列表
  get_horn_source_enum: 'get /common/param/v1/get_param_config',
  post_horn_set_config: 'post /horn/v1/set_config',
  // 高端理财 合格投资者认证审核
  get_investors_list: `get /investors/v1/investors_list`, // 获取审核列表
  get_investors_reasons: `get /common/param/v1/get_param`, // 获取审核失败原因下拉
  investors_check: `post /investors/v1/investors_check`, // 客服审核
  get_s3_image_base64: `get /common/s3/v1/s3_image_base64`, // 获取审核失败原因下拉
  // 售前客服体系
  get_trade_action_param: `get /trade_action/v1/query_param`, // 获取断点线索池参数
  get_trade_action_list: `post /trade_action/v1/query_order_messages`, // 获取断点线索池
  handle_trade_action: `get /trade_action/v1/handle`, // 断点线索池处理

  // 网银转账
  get_method_list: `get /pay/gt/v1/capital_method`, //获取渠道列表
  get_banktransfer_record: `get /pay/gt/v1/bank_transfer_query`, //获取转账记录
  update_banktransfer_record: `get /pay/gt/v1/bank_transfer_improve`, //更新转账记录
  download_banktransfer_record: `getFile /pay/gt/v1/zjdz_download`,
  // 断点开户
  get_openaccount_action_param: `get /openaccount/status/list`, // 获取断点线索池参数
  get_openaccount_action_list: `post /openaccount/form`, // 获取断点线索池
  get_openaccount_action_list_v2: 'post /openaccount/v2/form', // 获取断点线索池，表单信息查询
  handle_openaccount_action: `post /openaccount/status/define`, // 断点线索池处理
  handle_openaccount_action_v2: `post /openaccount/v2/status/define`, // 断点线索池处理，客户状态定义
  handle_openaccount_modify_division: `post /openaccount/modify_division`, // 指定分配
  handle_openaccount_modify_division_v2: 'post /openaccount/v2/modify_division', // 向指定销售分配用户，指定分配销售
  get_division_sale_ids: `get /openaccount/sale_ids/get`, // 获取平均分配的id配置
  handle_division_sale_ids: `post /openaccount/sale_ids/define`, // 操作平均分配的id配置
  get_bread_point_list: 'get /openaccount/v2/break_point/list', // 获取断点环节列表
  get_open_track_list: 'get /openaccount/v2/open_track/list', // 获取开户来源列表
  post_record_call: 'post /openaccount/v2/dial_record', // 记录拨号
  getSallQueryRecord: 'get /sale_call/v2/query_record', // 查看用户联系明细

  /**
   * 电销相关
   */
  // 电销转化报表-销售
  queryTeleSaleSaleReport: `post /telesale/query/salereport/v1`, // 获取电销转化报表-销售维度

  // 网销组（理财助理组）业绩统计
  get_netsell_group_list: `get /salary/online/query/groups`, // 获取所有理财助理组的组名和id
  get_netsell_group_data: `post /salary/online/sales/list`, // 查询组员薪资列表
  get_netsell_manager_data: `get /salary/online/manager/list`, // 查询主管薪资列表
  update_heguixishu: `post /salary/online/update/heguixishu`, //修改合规系数
  getfile_salary_excel: `getFile /salary/online/export/salaryexcel`, //导出组员薪资
  getfile_manager_salary_excel: `getFile /salary/online/export/manager/salaryexcel`, // 导出主管薪资
  get_shougou_detail: `post /salary/online/detail/shougou`, //查询首购详情
  get_wanyuanhu_detail: `post /salary/online/detail/wanyuanhu`, //查询万元户详情
  get_youhu_detail: `post /salary/online/detail/youhu`, //查询优户详情
  queryDatabaseQueryTerm: `get /customer/database/v1/query_term`, //获取资料库检索条件
  QueryPositionLift: `get /query/term/v1/position_life_cycle`, // 获取持仓声明周期标签选项

  // 线索3.0
  fileUpload: 'uploadFile /tracking/data3/fileUpload', // 文件上传
  fileDownload: 'getFile /tracking/data3/fileDownload', // 文件下载
  queryStrategy: 'post /tracking/data3/queryStrategy', // 查询单个策略
  updateStrategy: 'post /tracking/data3/updateStrategy', // 更新策略
  addStrategy: 'post /tracking/data3/addStrategy', // 新增策略
  userList: 'post /tracking/data3/user/list', // 获取olas用户分类
  queryintelligentlistlabel: 'get /tracking/data3/cbas/queryintelligentlistlabel', // 查询kyc labels列表
  downloadTemplate: 'getFile /tracking/data3/downloadTemplate',
  breakPointV2: 'get /openaccount/v2/break_point2/list', //获取版本断点信息V2

  // 智能外呼线索池
  saveOperateLog: 'post /customer/intention/v1/save_operate_log', // 销售操作记录保存

  // 券活动相关
  checkCouponTemplateNameIsDuplicate:'get /coupon/template/v1/check_duplicate_coupon_template_name',
  checkCouponActiveNameIsDuplicate:'get /coupon/template/v1/check_duplicate_coupon_name',
  // 查询垫资限额数据
  queryAdvanceaLimit: 'get /advance/limit/query',
  // 获取OA相关信息
  getOACommonUserInfo: "get /oa/sale_info/v1",

 /**
   * 成本项管理
   * http://yapi.myhexin.com/yapi/service/311188/interface/api/cat_398061
   * http://yapi.myhexin.com/yapi/service/311188/interface/api/cat_397545
   */
  // 查询投管人
  getOrganization: 'get /fof/selector/v1/organization',
  // 查询投资账户
  getInvestAccount: 'get /fof/selector/v1/product',
  // 添加成本信息
  addCostInfo: 'post /costInfo/add',
  // 文件上传
  uploadCostFile: 'post /fofComment/upload',
  // 文件预览
  viewCostFile: 'get /fofComment/downLoad',
  // 修改成本信息
  updateCostInfo: 'post /costInfo/update',
  // 删除成本信息
  delCostInfo: 'post /costInfo/delete',
  // 查询成本信息列表
  getCostList: 'post /costInfo/queryList',
  // 查询成本信息单条详情
  getCostDetails: 'get /costInfo/queryById',
  // 导出成本信息
  exportCostInfo: 'getFile /costInfo/export',
  // 查询审核列表
  getApprovalList: 'post /fof/cost/approval/list',
  // 审核内容查看
  getApprovalInner: 'post /fof/cost/approval/query',
  // 审核内容操作
  operateApprovalInner: 'post /fof/cost/approval/operate',
  // 查询投资账户配置
  getDivideList: 'post /fof/cost/divide/query',
  // 投资账户配置新增
  addDivide: 'post /fof/cost/divide/add',
  // 投资账户配置导出
  exportDivide: 'getFile /fof/cost/divide/export',
  // 查询投资账户配置销售人员
  getDivideSaleList: 'get /fof/selector/v1/saleinfo',
};

export default function (
  Interface: string,
  data?: any,
  params?: any,
  config?: any
): Promise<any> {
  const request = INTERFACES[Interface].split(' ');
  const method = request[0];
  let url = request[1];

  // 导出文件
  if (method === 'getFile') {
    let query = '?';
    for (const key in data) {
      query += `${key}=${data[key]}&`;
    }
    url += query.slice(0, -1);
  }

  return ajax[method](url, data, params, config);
}
