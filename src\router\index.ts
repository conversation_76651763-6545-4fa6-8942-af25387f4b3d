import { createRouter, createWebHashHistory, RouteRecordRaw } from 'vue-router';
import { createRouterGuards } from './router-guards';
import CustomLayout from '@/layout/customLayout.vue';

import 'nprogress/css/nprogress.css'; // 进度条样式

const routes = [
  {
    path: '',
    hidden: true,
    redirect: '/index',
  },
  {
    path: '/login',
    name: 'login',
    hidden: true,
    component: () => import('@/views/login.vue'),
    meta: {
      title: '登陆',
    },
  },
  {
    path: '/customer-service',
    name: '意见反馈中心',
    component: CustomLayout,
    children: [
            {
              path: 'simu-feedback',
              name: 'simu-feedback',
              meta: {
                title: '私募回访',
              },
              component: () => import('@/views/customer-service/simu/index.vue'),
            },
    ],
  },
  // 高端理财相关
  {
    path: '/highFinancial',
    name: '高端理财相关',
    component: CustomLayout,
    children: [
      {
        path: 'investorCertificate',
        name: 'investorCertificate',
        meta: {
          title: '网销业绩统计',
        },
        component: () =>
          import('@/views/highFinancial/investorCertificate/index.vue'),
      },
    ],
  },
  {
    path: '/packet',
    name: '红包后台',
    component: CustomLayout,
    children: [
      // 活动列表页
      {
        path: 'activities',
        name: 'packetActivities',
        meta: {
          title: '活动项目列表',
        },
        component: () => import('@/views/packet/activities/index.vue'),
      },
      // 活动列表审核页
      {
        path: 'verify',
        name: 'packetVerify',
        meta: {
          title: '活动项目审核',
        },
        component: () => import('@/views/packet/activities/index.vue'),
      },
    ]
  },
  // 工具权益相关
  {
    path: '/instrumentEquity',
    name: 'instrumentEquity',
    component: CustomLayout,
    children: [
      {
        path: 'rightsGive',
        name: 'rightsGive',
        meta: {
          title: '工具权益发放',
        },
        component: () =>
          import('@/views/instrumentEquity/rightsGive/index.vue'),
      },
    ],
  },
  {
    path: '/ijjProductPool',
    name: 'ijjProductPool',
    meta: {
      title: '爱基金产品池',
    },
    component: CustomLayout,
    children: [
      {
        path: 'marketingPool',
        name: 'marketingPool',
        meta: {
          title: '营销池',
        },
        children: [
          {
            path: '',
            name: '',
            meta: {
              title: '营销池',
            },
            component: () =>
              import('@/views/ijjProductPool/marketingPool/index.vue'),
          },
          {
            path: 'detail',
            name: 'detail',
            meta: {
              title: '营销池详情页',
            },
            component: () =>
              import('@/views/ijjProductPool/marketingPool/detail/index.vue'),
          },
          {
            path: 'records',
            name: 'records',
            meta: {
              title: '营销池操作记录',
            },
            component: () =>
              import('@/views/ijjProductPool/marketingPool/records/index.vue'),
          },
        ],
      },
      {
        path: 'highProductPool',
        name: 'highProductPool',
        meta: {
          title: '高端产品池',
        },
        component: () =>
          import('@/views/ijjProductPool/highProductPool/index.vue'),
      },
      {
        path: 'productDetail',
        name: 'productDetail',
        meta: {
          title: '产品详情页',
        },
        component: () =>
          import('@/views/ijjProductPool/productDetail/index.vue'),
      },
    ],
  },
];

// const routes1 = [
//   {
//     path: '',
//     hidden: true,
//     redirect: '/index',
//   },
//   {
//     path: '/strategy-detail',
//     name: '策略详情',
//     component: () => import('@/views/strategy-detail/index.vue'),
//     meta: {
//       title: '策略详情',
//     },
//   },
//   {
//     path: '/fund/home',
//     name: '首页数据看板',
//     component: () => import('@/views/homeV2/index.vue'),
//     meta: {
//       title: '首页数据看板',
//     },
//   },
//   {
//     path: '/login',
//     name: 'login',
//     hidden: true,
//     component: () => import('@/views/login.vue'),
//     meta: {
//       title: '登陆',
//     },
//   },
//   {
//     path: '/system',
//     name: 'system',
//     meta: {
//       title: '系统信息',
//     },
//     component: CustomLayout,
//     children: [
//       {
//         path: 'black-table',
//         name: 'black-table',
//         meta: {
//           title: '查询黑名单',
//         },
//         component: () =>
//           import('@/views/systemInfo/blackMenu/BlackMenuTable.vue'),
//       },
//       {
//         path: 'black-add',
//         name: 'black-add',
//         meta: {
//           title: '导入黑名单',
//         },
//         component: () =>
//           import('@/views/systemInfo/blackMenu/BlackMenuAdd.vue'),
//       },
//     ],
//   },
//   {
//     path: '/customer',
//     name: '客户相关',
//     meta: {
//       title: '客户相关',
//     },
//     component: CustomLayout,
//     children: [
//       {
//         path: 'distribution',
//         name: 'distribution',
//         meta: {
//           title: '客户分配',
//         },
//         component: () => import('@/views/customer/distribution/index.vue'),
//       },
//       {
//         path: 'list',
//         name: 'customerList',
//         meta: {
//           title: '客户列表',
//         },
//         component: () => import('@/views/customer/list/index.vue'),
//       },
//       {
//         path: 'info',
//         name: 'customerInfo',
//         meta: {
//           title: '客户信息',
//         },
//         component: () => import('@/views/customer/detail/index.vue'),
//       },
//       {
//         path: 'specificInfo',
//         name: 'customerSpecificInfo',
//         meta: {
//           title: '客户具体明细',
//         },
//         component: () => import('@/views/customer/specificInfo/index.vue'),
//       },
//       {
//         path: 'resource',
//         name: 'customerResource',
//         meta: {
//           title: '客户资料查询',
//         },
//         component: () => import('@/views/customer/resource/index.vue'),
//       },
//       {
//         path: 'noOpenAccountDetail',
//         name: 'noOpenAccountDetail',
//         meta: {
//           title: '未开户用户详情页',
//         },
//         component: () =>
//           import('@/views/customer/noOpenAccountDetail/index.vue'),
//       },
//     ],
//   },
//   {
//     path: '/market',
//     name: '销售相关',
//     meta: {
//       title: '销售相关',
//     },
//     component: CustomLayout,
//     children: [
//       {
//         path: 'list',
//         name: 'marketList',
//         meta: {
//           title: '销售列表',
//         },
//         component: () => import('@/views/market/list/list.vue'),
//       },
//       {
//         path: 'kpi',
//         name: 'marketKpi',
//         meta: {
//           title: '业绩统计',
//         },
//         component: () => import('@/views/market/kpi-census/index.vue'),
//       },
//       {
//         path: 'tel',
//         name: 'marketTel',
//         meta: {
//           title: '电话统计',
//         },
//         component: () => import('@/views/market/tel-census/index.vue'),
//       },
//       {
//         path: 'detail',
//         name: 'saleDetail',
//         meta: {
//           title: '销售详情页面',
//         },
//         component: () =>
//           import('@/views/market/sale-detail/sale-detail-main.vue'),
//       },
//       {
//         path: 'activity',
//         name: 'saleActivity',
//         meta: {
//           title: '销售活动页',
//         },
//         component: () => import('@/views/market/activity/index.vue'),
//       },
//       {
//         path: 'clues-push',
//         name: 'cluesPush',
//         meta: {
//           title: '电销线索推送',
//         },
//         component: () => import('@/views/market/cluesPush/index.vue'),
//       },
//       // 2022.6.16 FW-4267 销售新版分成方案
//       {
//         path: 'new-kpi',
//         component: CustomLayout,
//         children: [
//           {
//             path: 'personal',
//             name: 'new-kpi-personal',
//             meta: {
//               title: '个人销售业绩统计页',
//             },
//             component: () =>
//               import('@/views/market/new-kpi/view/PersonalKpiTable.vue'),
//           },
//           {
//             path: 'personal-detail',
//             name: 'personal-detail',
//             meta: {
//               title: '销售业绩提成明细',
//             },
//             component: () =>
//               import('@/views/market/new-kpi/view/personalDetail.vue'),
//           },
//           {
//             path: 'group',
//             name: 'new-kpi-group',
//             meta: {
//               title: '小组销售业绩统计页',
//             },
//             component: () =>
//               import('@/views/market/new-kpi/view/GroupKpiTable.vue'),
//           },
//           {
//             path: 'manager-set',
//             name: 'new-kpi-manager-set',
//             meta: {
//               title: '销售主管考核人数',
//             },
//             component: () =>
//               import('@/views/market/new-kpi/view/ManagerKpiSet.vue'),
//           },
//         ],
//       },
//       {
//         path: 'salary-list',
//         name: 'salary-list-info',
//         meta: {
//           title: '电销工资单',
//         },
//         component: () => import('@/views/market/salaryListInfo/index.vue'),
//       },
//       // 2023.04.18 新增理财助理组业绩报表
//       {
//         path: 'financial-assistant',
//         component: CustomLayout,
//         children: [
//           {
//             path: 'personal',
//             name: 'financial-assistant-personal',
//             meta: {
//               title: '理财助理组个人业绩统计',
//             },
//             component: () =>
//               import('@/views/market/financial-assistant/PersonalKpi.vue'),
//           },
//           {
//             path: 'manager',
//             name: 'financial-assistant-manager',
//             meta: {
//               title: '理财助理组主管业绩统计',
//             },
//             component: () =>
//               import('@/views/market/financial-assistant/ManagerKpi.vue'),
//           },
//         ],
//       },
//     ],
//   },
//   {
//     path: '/person',
//     name: '人事相关',
//     meta: {
//       title: '人事相关',
//     },
//     component: CustomLayout,
//     children: [
//       {
//         path: 'salary',
//         name: 'salary',
//         meta: {
//           title: '工资核算',
//         },
//         component: () => import('@/views/person/salary/index.vue'),
//       },
//       {
//         path: 'salary-config',
//         name: 'salary-config',
//         meta: {
//           title: '工资单配置后台',
//         },
//         component: () => import('@/views/person/salaryConfig/index.vue'),
//       },
//       // {
//       //   path: 'position',
//       //   name: 'positionInfo',
//       //   meta: {
//       //     title: '岗位信息设置',
//       //   },
//       //   component: () => import('@/views/person/position/index.vue'),
//       // },
//       // {
//       //   path: 'account',
//       //   name: 'financeAccount',
//       //   meta: {
//       //     title: '财务核算',
//       //   },
//       //   component: () => import('@/views/person/finance-account/index.vue'),
//       // }
//     ],
//   },
//   {
//     path: '/operate',
//     name: '运营相关',
//     meta: {
//       title: '运营相关',
//     },
//     component: CustomLayout,
//     children: [
//       {
//         path: 'production',
//         name: 'production',
//         meta: {
//           title: '产品列表',
//         },
//         component: () => import('@/views/operate/production.vue'),
//       },
//     ],
//   },
//   // TODO 2022.3.8 意见反馈
//   {
//     path: '/customer-service',
//     name: '意见反馈中心',
//     component: CustomLayout,
//     children: [
//       {
//         path: 'feedback',
//         name: 'feedback',
//         meta: {
//           title: '意见发聩',
//         },
//         component: () => import('@/views/customer-service/feedback/index.vue'),
//       },
//       // {
//       //   path: 'simu-feedback',
//       //   name: 'simu-feedback',
//       //   meta: {
//       //     title: '私募回访',
//       //   },
//       //   component: () => import('@/views/customer-service/simu/index.vue'),
//       // },
//       {
//         path: 'personalCuePool',
//         name: 'personalCuePool',
//         meta: {
//           title: '个人线索池',
//         },
//         component: () =>
//           import('@/views/customer-service/personalCuePool/index.vue'),
//       },
//       {
//         path: 'publicCuePool',
//         name: 'publicCuePool',
//         meta: {
//           title: '公共线索池',
//         },
//         component: () =>
//           import('@/views/customer-service/publicCuePool/index.vue'),
//       },
//       {
//         path: 'cuePoolContinueOld',
//         name: 'cuePoolContinueOld',
//         meta: {
//           title: '未开户线索池承接页',
//         },
//         component: () =>
//           import('@/views/customer-service/cuePoolContinueOld/index.vue'),
//       },
//       {
//         path: 'cuePoolContinue',
//         name: 'cuePoolContinue',
//         meta: {
//           title: '未开户线索池承接页',
//         },
//         component: () =>
//           import('@/views/customer-service/cuePoolContinue/index.vue'),
//       },
//       {
//         path: 'cuePoolContinueV3',
//         name: 'cuePoolContinueV3',
//         meta: {
//           title: '未开户线索池承接页',
//         },
//         component: () =>
//           import('@/views/customer-service/cuePoolContinueV3/index.vue'),
//       },
//       {
//         path: 'noOpenAccoDistPool',
//         name: 'noOpenAccoDistPool',
//         meta: {
//           title: '未开户用户分配池',
//         },
//         component: () =>
//           import('@/views/customer-service/noOpenAccoDistPool/index.vue'),
//       },
//       {
//         path: 'recylePublicity',
//         name: 'recylePublicity',
//         meta: {
//           title: '资料回收公示',
//         },
//         component: () =>
//           import('@/views/customer-service/recylePublicity/index.vue'),
//       },
//       {
//         path: 'recyleAppeal',
//         name: 'recyleAppeal',
//         meta: {
//           title: '资料回收申诉',
//         },
//         component: () =>
//           import('@/views/customer-service/recyleAppeal/index.vue'),
//       },
//       {
//         path: 'walletConvert',
//         name: 'walletConvert',
//         meta: {
//           title: '钱包底层货币还原',
//         },
//         component: () =>
//           import('@/views/customer-service/walletConvert/index.vue'),
//       },
//     ],
//   },
//   // TODO 2022.3.8 销售任务中心
//   // {
//   //   path: '/task-center',
//   //   name: '销售任务中心',
//   //   component: CustomLayout,
//   //   children: [
//   //     {
//   //       path: '',
//   //       name: 'taskCenter',
//   //       meta: {
//   //         title: '销售任务中心',
//   //       },
//   //       component: () => import('@/views/task-center/index.vue'),
//   //     },
//   //   ],
//   // },
//   // 红包后台活动列表
//   {
//     path: '/packet',
//     name: '红包后台',
//     component: CustomLayout,
//     children: [
//       // 活动列表页
//       {
//         path: 'activities',
//         name: 'packetActivities',
//         meta: {
//           title: '活动项目列表',
//         },
//         component: () => import('@/views/packet/activities/index.vue'),
//       },
//       // 活动列表审核页
//       {
//         path: 'verify',
//         name: 'packetVerify',
//         meta: {
//           title: '活动项目审核',
//         },
//         component: () => import('@/views/packet/activities/index.vue'),
//       },
//       // 红包使用查询
//       {
//         path: 'red-query',
//         name: 'redQuery',
//         meta: {
//           title: '红包使用查询',
//         },
//         component: () => import('@/views/packet/red-query/index.vue'),
//       },
//       // 销售统计
//       {
//         path: 'sale/statistic',
//         name: 'saleStatistic',
//         meta: {
//           title: '销售统计',
//         },
//         component: () => import('@/views/packet/sale-statistic/index.vue'),
//       },
//       // 销售红包库
//       {
//         path: 'sale/red',
//         name: 'saleRed',
//         meta: {
//           title: '销售红包库',
//         },
//         component: () => import('@/views/packet/sale-red/index.vue'),
//       },
//       // 券模板
//       /** 券模板列表 */
//       {
//         path: 'coupon/template/list',
//         name: 'couponTemplateList',
//         meta: {
//           title: '券模板列表',
//         },
//         component: () => import('@/views/packet/coupon/CouponTemplateList.vue'),
//       },
//       /** 券模板审核 */
//       {
//         path: 'coupon/template/verify',
//         name: 'couponTemplateVerify',
//         meta: {
//           title: '券模板列表',
//         },
//         component: () => import('@/views/packet/coupon/CouponTemplateList.vue'),
//       },
//       /** 券模板详情 */
//       {
//         path: 'coupon/template/detail',
//         name: 'couponTemplateDetail',
//         meta: {
//           title: '券模板详情',
//         },
//         component: () =>
//           import('@/views/packet/coupon/CouponTemplateDetail.vue'),
//       },
//       /** 券活动列表 */
//       {
//         path: 'coupon/list',
//         name: 'couponList',
//         meta: {
//           title: '券活动列表',
//         },
//         component: () => import('@/views/packet/coupon/CouponList.vue'),
//       },
//       /** 券活动详情 */
//       {
//         path: 'coupon/detail',
//         name: 'couponDetail',
//         meta: {
//           title: '券活动详情',
//         },
//         component: () => import('@/views/packet/coupon/CouponDetail.vue'),
//       },
//       // 明细页
//       // 领取明细
//       {
//         path: 'detail/receive',
//         name: 'receiveDetail',
//         meta: {
//           title: '领取明细',
//         },
//         component: () => import('@/views/packet/details/ReceiveDetail.vue'),
//       },
//       // 使用明细
//       {
//         path: 'detail/use',
//         name: 'useDetail',
//         meta: {
//           title: '使用明细',
//         },
//         component: () => import('@/views/packet/details/UseDetail.vue'),
//       },
//       // 红包明细
//       {
//         path: 'detail/red',
//         name: 'redDetail',
//         meta: {
//           title: '红包明细',
//         },
//         component: () => import('@/views/packet/details/RedDetail.vue'),
//       },
//       //核销总表查询
//       {
//         path: 'financialQueryTotal',
//         name: 'financialQueryTotal',
//         meta: {
//           title: '核销总表查询',
//         },
//         component: () =>
//           import('@/views/packet/financial-query-total/index.vue'),
//       },
//       //活动经费查询
//       {
//         path: 'activityQueryByTime',
//         name: 'activityQueryByTime',
//         meta: {
//           title: '核销总表查询',
//         },
//         component: () =>
//           import('@/views/packet/activity-query-by-time/index.vue'),
//       },
//     ],
//   },
//   // 财务相关
//   {
//     path: '/financial',
//     name: '财务相关',
//     component: CustomLayout,
//     children: [
//       {
//         path: 'reconciliateReport',
//         name: 'reconciliateReport',
//         meta: {
//           title: '对账表',
//         },
//         component: () =>
//           import('@/views/financial/reconciliateReport/index.vue'),
//       },
//       {
//         path: 'reconciliateReport/detail',
//         name: 'reconciliateReport-detail',
//         meta: {
//           title: '对账表详情页',
//         },
//         component: () =>
//           import('@/views/financial/reconciliateReport/detail/index.vue'),
//       },
//       {
//         path: 'capitalFlowReport',
//         name: 'capitalFlowReport',
//         meta: {
//           title: '资金流水报表',
//         },
//         component: () =>
//           import('@/views/financial/capitalFlowReport/index.vue'),
//       },
//       {
//         path: 'capitalFlowReport/detail',
//         name: 'capitalFlowReport-detail',
//         meta: {
//           title: '资金流水明细',
//         },
//         component: () =>
//           import('@/views/financial/capitalFlowReport/detail/index.vue'),
//       },
//     ],
//   },
//   // 网销相关
//   {
//     path: '/netSale',
//     name: '网销相关',
//     component: CustomLayout,
//     children: [
//       {
//         path: 'kpiStatistics',
//         name: 'kpiStatistics',
//         meta: {
//           title: '网销业绩统计',
//         },
//         component: () => import('@/views/netSale/kpiStatistics/index.vue'),
//       },
//       {
//         path: 'kpiStatistics/detail',
//         name: 'kpiStatistics-detail',
//         meta: {
//           title: '网销对应提成客户表',
//         },
//         component: () =>
//           import('@/views/netSale/kpiStatistics/detail/index.vue'),
//       },
//       {
//         path: 'distributePool',
//         name: 'distributePool',
//         meta: {
//           title: '归属网销分配池',
//         },
//         component: () => import('@/views/netSale/distributePool/index.vue'),
//       },
//       {
//         path: 'netSaleConversionTable/clue',
//         name: 'netSaleConversionTable-clue',
//         meta: {
//           title: '网销线索转化报表-线索维度',
//         },
//         component: () =>
//           import('@/views/netSale/conversion-table/clue/index.vue'),
//       },
//       {
//         path: 'netSaleDatabase',
//         name: 'netSaleDatabase',
//         meta: {
//           title: '网销资料库',
//         },
//         component: () => import('@/views/netSale/netSaleDatabase/index.vue'),
//       },
//     ],
//   },
//   // 电销相关
//   {
//     path: '/eSale',
//     name: '电销相关',
//     component: CustomLayout,
//     children: [
//       {
//         path: 'conversionTable/clue',
//         name: 'conversionTable-clue',
//         meta: {
//           title: '电销转化报表-线索',
//         },
//         component: () =>
//           import('@/views/eSale/conversion-table/clue/index.vue'),
//       },
//       {
//         path: 'conversionTable/sale',
//         name: 'conversionTable-sale',
//         meta: {
//           title: '电销转化报表-销售',
//         },
//         component: () =>
//           import('@/views/eSale/conversion-table/sale/index.vue'),
//       },
//       {
//         path: 'eSaleDatabase',
//         name: 'eSaleDatabase',
//         meta: {
//           title: '电销资料库',
//         },
//         component: () => import('@/views/eSale/eSaleDatabase/index.vue'),
//       },
//     ],
//   },
//   // 小喇叭
//   {
//     path: '/smallTrumpet',
//     name: '小喇叭',
//     component: CustomLayout,
//     children: [
//       {
//         path: 'index',
//         name: 'config',
//         meta: {
//           title: '基金小喇叭',
//         },
//         component: () => import('@/views/smallTrumpet/config/index.vue'),
//       },
//     ],
//   },
//   // 高端理财相关
//   {
//     path: '/highFinancial',
//     name: '高端理财相关',
//     component: CustomLayout,
//     children: [
//       {
//         path: 'investorCertificate',
//         name: 'investorCertificate',
//         meta: {
//           title: '网销业绩统计',
//         },
//         component: () =>
//           import('@/views/highFinancial/investorCertificate/index.vue'),
//       },
//     ],
//   },
//   // 网银相关
//   {
//     path: '/bankTransfer',
//     name: '网银转账',
//     component: CustomLayout,
//     children: [
//       {
//         path: 'index',
//         name: 'index',
//         meta: {
//           title: '网银转账对账表',
//         },
//         component: () => import('@/views/bankTransfer/index.vue'),
//       },
//     ],
//   },
//   {
//     path: '/clueStrategy',
//     name: 'clueStrategy',
//     meta: {
//       title: '线索策略',
//     },
//     component: CustomLayout,
//     children: [
//       {
//         path: 'list',
//         name: 'list',
//         meta: {
//           title: '策略列表',
//         },
//         component: () => import('@/views/clueStrategy/list/index.vue'),
//       },
//     ],
//   },
//   {
//     path: '/ijjProductPool',
//     name: 'ijjProductPool',
//     meta: {
//       title: '爱基金产品池',
//     },
//     component: CustomLayout,
//     children: [
//       {
//         path: 'marketingPool',
//         name: 'marketingPool',
//         meta: {
//           title: '营销池',
//         },
//         children: [
//           {
//             path: '',
//             name: '',
//             meta: {
//               title: '营销池',
//             },
//             component: () =>
//               import('@/views/ijjProductPool/marketingPool/index.vue'),
//           },
//           {
//             path: 'detail',
//             name: 'detail',
//             meta: {
//               title: '营销池详情页',
//             },
//             component: () =>
//               import('@/views/ijjProductPool/marketingPool/detail/index.vue'),
//           },
//           {
//             path: 'records',
//             name: 'records',
//             meta: {
//               title: '营销池操作记录',
//             },
//             component: () =>
//               import('@/views/ijjProductPool/marketingPool/records/index.vue'),
//           },
//         ],
//       },
//       {
//         path: 'productDetail',
//         name: 'productDetail',
//         meta: {
//           title: '产品详情页',
//         },
//         component: () =>
//           import('@/views/ijjProductPool/productDetail/index.vue'),
//       },
//     ],
//   },
//   // 机构相关
//   {
//     path: '/organization',
//     name: '机构相关',
//     component: CustomLayout,
//     children: [
//       {
//         path: 'investManagerInfo',
//         name: 'investManagerInfo',
//         meta: {
//           title: '投管人信息',
//         },
//         component: () =>
//           import('@/views/organization/investManagerInfo/index.vue'),
//       },
//       {
//         path: 'customerInfo',
//         name: 'customerInfoOrg',
//         meta: {
//           title: '客户信息',
//         },
//         component: () => import('@/views/organization/customerInfo/index.vue'),
//       },
//       {
//         path: 'advanceAudit',
//         name: 'advanceAudit',
//         meta: {
//           title: '垫资审核',
//         },
//         component: () => import('@/views/organization/advanceAudit/index.vue'),
//       },
//       {
//         path: 'visitRecord',
//         name: 'visitRecord',
//         meta: {
//           title: '拜访记录',
//         },
//         component: () => import('@/views/organization/visitRecord/index.vue'),
//       },
//       {
//         path: 'modifyLimit',
//         name: 'modifyLimit',
//         meta: {
//           title: '限额修改',
//         },
//         component: () => import('@/views/organization/modifyLimit/index.vue'),
//       },
//       {
//         path: 'costRegister',
//         name: 'costRegister',
//         meta: {
//           title: '成本项登记',
//         },
//         component: () => import('@/views/organization/costRegister/index.vue'),
//       },
//       {
//         path: 'investAccount',
//         name: 'investAccount',
//         meta: {
//           title: '投资账户管理',
//         },
//         component: () => import('@/views/organization/investAccount/index.vue'),
//       },
//       {
//         path: 'costInvestAudit',
//         name: 'costInvestAudit',
//         meta: {
//           title: '审核列表',
//         },
//         component: () =>
//           import('@/views/organization/costInvestAudit/index.vue'),
//       },
//     ],
//   },
//   // 智能外呼线索池
//   {
//     path: '/intelCallCluePool',
//     name: 'intelCallCluePool',
//     meta: {
//       title: '智能外呼线索池',
//     },
//     component: () => import('@/views/intelCallCluePool/index.vue'),
//   },
//   // OA排行
//   {
//     path: '/OARank',
//     name: 'OARank',
//     component: () => import('@/views/OARank/index.vue'),
//   },
//   // 工具权益相关
//   {
//     path: '/instrumentEquity',
//     name: 'instrumentEquity',
//     component: CustomLayout,
//     children: [
//       {
//         path: 'rightsGive',
//         name: 'rightsGive',
//         meta: {
//           title: '工具权益发放',
//         },
//         component: () =>
//           import('@/views/instrumentEquity/rightsGive/index.vue'),
//       },
//     ],
//   },
// ];

const router = createRouter({
  // process.env.BASE_URL
  history: createWebHashHistory(''),
  routes: routes as Array<RouteRecordRaw>,
});

createRouterGuards(router);

export default router;
