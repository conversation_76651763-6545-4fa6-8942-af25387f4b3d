Arguments: 
  D:\ThsSoftware\nodejs1617_ths\ths\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\yarn\bin\yarn.js

PATH: 
  C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\ThsSoftware\nodejs1617_ths\ths\;C:\ThsSoftware\Git_ths\ths\cmd;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\Downloads\platform-tools;C:\Users\<USER>\Downloads\nvm-noinstall;D:\ThsSoftware\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\Downloads\platform-tools-latest-windows\platform-tools;

Yarn version: 
  1.22.19

Node version: 
  16.17.1

Platform: 
  win32 x64

Trace: 
  Error: https://miniapp.10jqka.com.cn/npmserver/ripemd160/-/ripemd160-2.0.1.tgz: Request failed "502 Bad Gateway"
      at ResponseError.ExtendableBuiltin (C:\Users\<USER>\AppData\Roaming\npm\node_modules\yarn\lib\cli.js:696:66)
      at new ResponseError (C:\Users\<USER>\AppData\Roaming\npm\node_modules\yarn\lib\cli.js:802:124)
      at Request.<anonymous> (C:\Users\<USER>\AppData\Roaming\npm\node_modules\yarn\lib\cli.js:66215:16)
      at Request.emit (node:events:513:28)
      at Request.module.exports.Request.onRequestResponse (C:\Users\<USER>\AppData\Roaming\npm\node_modules\yarn\lib\cli.js:141767:10)
      at ClientRequest.emit (node:events:513:28)
      at HTTPParser.parserOnIncomingClient (node:_http_client:674:27)
      at HTTPParser.parserOnHeadersComplete (node:_http_common:128:17)
      at TLSSocket.socketOnData (node:_http_client:521:22)
      at TLSSocket.emit (node:events:513:28)

npm manifest: 
  {
    "name": "hexin-tonghuashun-crm-vue3.0",
    "version": "0.1.0",
    "private": true,
    "scripts": {
      "start": "vue-cli-service serve",
      "build": "vue-cli-service build",
      "lint": "vue-cli-service lint"
    },
    "dependencies": {
      "@ag-grid-enterprise/all-modules": "~26.1.0",
      "@ant-design/icons-vue": "^6.0.1",
      "@king-fisher/crm-amis": "^0.1.5",
      "@king-fisher/amis-adapter": "^0.0.2",
      "@tinymce/tinymce-vue": "^4.0.4",
      "@vueup/vue-quill": "^1.0.0-beta.7",
      "@vueuse/core": "^5.3.0",
      "ag-grid-community": "^26.1.0",
      "ag-grid-vue3": "^26.1.2",
      "ant-design-vue": "^3.1.0-rc.3",
      "axios": "^0.21.1",
      "blueimp-md5": "^2.18.0",
      "clipboard": "^2.0.10",
      "clipboard-all": "^1.0.8",
      "currency.js": "^2.0.4",
      "dayjs": "^1.10.6",
      "dom-to-image": "^2.6.0",
      "events": "^3.3.0",
      "lodash": "^4.17.21",
      "moment": "^2.29.1",
      "nprogress": "^1.0.0-1",
      "postcss-px-to-viewport": "^1.1.1",
      "store": "^2.0.12",
      "vue": "3.2.33",
      "vue-class-component": "^8.0.0-rc.1",
      "vue-router": "^4.0.11",
      "vue2-org-tree": "^1.3.5",
      "vue3-quill": "^0.2.6",
      "vuex": "^4.0.2"
    },
    "devDependencies": {
      "@commitlint/cli": "^13.1.0",
      "@commitlint/config-conventional": "^13.1.0",
      "@types/lodash": "^4.14.172",
      "@types/node": "^16.6.0",
      "@types/webpack-env": "^1.16.2",
      "@typescript-eslint/eslint-plugin": "^4.31.0",
      "@typescript-eslint/parser": "^4.31.0",
      "@vue/babel-plugin-jsx": "^1.1.1",
      "@vue/cli-plugin-babel": "^4.5.13",
      "@vue/cli-plugin-eslint": "^4.5.13",
      "@vue/cli-plugin-router": "^4.5.13",
      "@vue/cli-plugin-typescript": "^4.5.13",
      "@vue/cli-plugin-vuex": "^4.5.13",
      "@vue/cli-service": "^4.5.13",
      "@vue/compiler-sfc": "3.2.33",
      "@vue/eslint-config-prettier": "^6.0.0",
      "@vue/eslint-config-typescript": "^7.0.0",
      "babel-plugin-import": "^1.13.3",
      "babel-plugin-lodash": "^3.3.4",
      "commitizen": "^4.2.4",
      "compression-webpack-plugin": "^8.0.1",
      "core-js": "^3.16.1",
      "eslint": "^7.32.0",
      "eslint-plugin-import": "^2.25.2",
      "eslint-plugin-prettier": "^4.0.0",
      "eslint-plugin-vue": "^7.17.0",
      "less": "^4.1.1",
      "less-loader": "5.0.0",
      "lint-staged": "^11.1.2",
      "lodash-webpack-plugin": "^0.11.6",
      "prettier": "^2.3.2",
      "pretty-quick": "^3.1.1",
      "sass": "^1.32.13",
      "sass-loader": "^10.0.2",
      "stylelint": "^13.13.1",
      "stylelint-config-prettier": "^8.0.2",
      "stylelint-config-standard": "^22.0.0",
      "stylelint-order": "^4.1.0",
      "stylelint-scss": "^3.20.1",
      "svg-sprite-loader": "^6.0.9",
      "typescript": "^4.3.5",
      "webpack": "^4.42.0"
    },
    "config": {
      "commitizen": {
        "path": "./node_modules/cz-customizable"
      }
    },
    "lint-staged": {
      "*.{vue,js,ts}": "eslint --fix"
    },
    "keywords": [
      "vue",
      "ant-design-vue",
      "vue3",
      "ts"
    ],
    "license": "myhexin",
    "homepage": "",
    "__npminstall_done": false
  }

yarn manifest: 
  No manifest

Lockfile: 
  No lockfile
